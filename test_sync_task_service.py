#!/usr/bin/env python3
"""
测试同步翻译任务服务
验证从多线程改为同步执行后的功能
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_sync_task_service():
    """测试同步任务服务的基本功能"""
    print("=" * 50)
    print("测试同步翻译任务服务")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from app.services.task_service import TranslationTaskService
        
        # 创建任务服务实例
        task_service = TranslationTaskService()
        
        print("✅ 成功创建 TranslationTaskService 实例")
        print(f"   类型: {type(task_service).__name__}")
        
        # 检查是否移除了多线程相关属性
        if not hasattr(task_service, 'active_tasks'):
            print("✅ 已移除 active_tasks 属性")
        else:
            print("❌ 仍然存在 active_tasks 属性")
            
        if not hasattr(task_service, 'timeout_timers'):
            print("✅ 已移除 timeout_timers 属性")
        else:
            print("❌ 仍然存在 timeout_timers 属性")
        
        # 检查方法是否存在
        methods_to_check = [
            'start_batch_translation',
            '_process_translation_task',
            '_translate_single_item',
            '_handle_translation_success',
            '_handle_translation_error',
            '_check_task_completion',
            'cancel_task'
        ]
        
        for method_name in methods_to_check:
            if hasattr(task_service, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                print(f"❌ 方法缺失: {method_name}")
        
        # 检查是否移除了超时相关方法
        removed_methods = ['_start_timeout_timer', '_handle_task_timeout', '_cleanup_task']
        for method_name in removed_methods:
            if not hasattr(task_service, method_name):
                print(f"✅ 已移除方法: {method_name}")
            else:
                print(f"❌ 仍然存在方法: {method_name}")
        
        print("\n🎯 同步任务服务特点:")
        print("   - 移除了多线程相关代码")
        print("   - 移除了超时定时器")
        print("   - 翻译任务同步执行")
        print("   - 简化了任务管理逻辑")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_method_signatures():
    """测试方法签名是否正确"""
    print("\n" + "=" * 50)
    print("测试方法签名")
    print("=" * 50)
    
    try:
        from app.services.task_service import TranslationTaskService
        import inspect
        
        task_service = TranslationTaskService()
        
        # 检查 start_batch_translation 方法签名
        sig = inspect.signature(task_service.start_batch_translation)
        params = list(sig.parameters.keys())
        expected_params = ['task_id', 'items', 'source_language']
        
        if params == expected_params:
            print("✅ start_batch_translation 方法签名正确")
        else:
            print(f"❌ start_batch_translation 方法签名错误: {params}")
        
        # 检查返回类型注解
        if sig.return_annotation == bool:
            print("✅ start_batch_translation 返回类型注解正确")
        else:
            print(f"❌ start_batch_translation 返回类型注解错误: {sig.return_annotation}")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法签名测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success1 = test_sync_task_service()
    success2 = test_method_signatures()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！同步翻译任务服务修改成功")
    else:
        print("❌ 部分测试失败，请检查修改")
    print("=" * 50)
