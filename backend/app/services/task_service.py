"""
同步翻译任务处理服务
"""

import logging
from typing import Dict, Any, List

from app.models.task_models import TaskItemStatus
from app.services.redis_service import redis_service
from app.services.websocket_service import websocket_service
from app.services.translate_service import TranslateService
from app.helpers.task_manager import TaskManager

logger = logging.getLogger(__name__)


class TranslationTaskService:
    """同步翻译任务处理服务"""

    def __init__(self):
        self.translate_service = TranslateService()
        self.task_manager = TaskManager()

    def start_batch_translation(
        self, task_id: str, items: List[Dict[str, Any]], source_language: str = "en-US"
    ) -> bool:
        """启动批量翻译任务（同步执行）"""
        try:
            # 创建任务数据
            item_keys = [item["key"] for item in items]
            task_data = self.task_manager.create_task_data(task_id, item_keys)

            # 保存到Redis
            if not redis_service.create_task(task_data):
                logger.error(f"创建任务记录失败: {task_id}")
                return False

            # 同步执行翻译任务
            self._process_translation_task(task_id, items, source_language)

            logger.info(f"完成批量翻译任务: {task_id}, 共 {len(items)} 条记录")
            return True

        except Exception as e:
            logger.error(f"批量翻译任务失败: {str(e)}")
            return False

    def _process_translation_task(
        self, task_id: str, items: List[Dict[str, Any]], source_language: str
    ):
        """处理翻译任务的主函数（同步执行）"""
        try:
            logger.info(f"_process_translation_task items: {items}")
            for item in items:
                try:
                    self._translate_single_item(task_id, item, source_language)
                except Exception as e:
                    logger.error(
                        f"翻译单个条目失败: {item.get('key', 'unknown')}, error: {str(e)}"
                    )
                    self._handle_translation_error(
                        task_id, item.get("key", "unknown"), str(e)
                    )

            # 检查任务是否完成
            self._check_task_completion(task_id)

        except Exception as e:
            logger.error(f"处理翻译任务异常: {task_id}, error: {str(e)}")
            raise

    def _translate_single_item(
        self, task_id: str, item: Dict[str, Any], source_language: str
    ):
        """翻译单个条目"""
        key = item["key"]
        source_text = item["source_text"]
        target_languages = item["target_languages"]

        logger.info(f"开始翻译条目: {key}")

        # 如果源文本为空，返回空翻译结果
        if not source_text or not source_text.strip():
            translations = {lang: "" for lang in target_languages}
            self._handle_translation_success(task_id, key, translations)
            return

        try:
            # 执行翻译
            translations = self.translate_service.translate_content_batch(
                source_text=source_text,
                target_languages=target_languages,
                source_language=source_language,
            )

            # 处理翻译结果
            self._handle_translation_success(task_id, key, translations)
            logger.info(f"翻译完成: {key}")

        except Exception as e:
            # 翻译失败，抛出异常让上层处理
            error_msg = str(e)
            logger.error(f"翻译条目失败: {key}, 错误: {error_msg}")
            raise Exception(error_msg)

    def _handle_translation_success(
        self, task_id: str, item_key: str, translations: Dict[str, str]
    ):
        """处理翻译成功"""
        try:
            # 更新Redis中的任务状态
            task_data = redis_service.get_task(task_id)
            if not task_data:
                logger.error(f"获取任务数据失败: {task_id}")
                return

            # 更新条目状态
            updated_task_data = self.task_manager.update_item_status(
                task_data,
                item_key,
                TaskItemStatus.SUCCESS,
                result={"translations": translations},
            )

            # 保存到Redis
            redis_service.update_task(updated_task_data)

            # 发送WebSocket消息
            progress = self.task_manager.get_progress(updated_task_data)
            websocket_service.send_translation_update(
                task_id=task_id,
                item_key=item_key,
                status=TaskItemStatus.SUCCESS,
                result={"translations": translations},
                progress=progress,
            )

        except Exception as e:
            logger.error(f"处理翻译成功时出错: {str(e)}")

    def _handle_translation_error(
        self, task_id: str, item_key: str, error_message: str
    ):
        """处理翻译失败"""
        try:
            # 更新Redis中的任务状态
            task_data = redis_service.get_task(task_id)
            if not task_data:
                logger.error(f"获取任务数据失败: {task_id}")
                return

            # 更新条目状态
            updated_task_data = self.task_manager.update_item_status(
                task_data, item_key, TaskItemStatus.FAILED, error_message=error_message
            )

            # 保存到Redis
            redis_service.update_task(updated_task_data)

            # 发送WebSocket消息
            progress = self.task_manager.get_progress(updated_task_data)
            websocket_service.send_translation_update(
                task_id=task_id,
                item_key=item_key,
                status=TaskItemStatus.FAILED,
                progress=progress,
                error_message=error_message,
            )

        except Exception as e:
            logger.error(f"处理翻译失败时出错: {str(e)}")

    def _check_task_completion(self, task_id: str):
        """检查任务是否完成"""
        try:
            task_data = redis_service.get_task(task_id)

            logger.info(f"task_data: {task_data}")
            if not task_data:
                return

            if task_data.status == "completed":
                # 发送任务完成消息
                summary = {
                    "total": task_data.total_items,
                    "completed": task_data.completed_items,
                    "failed": task_data.failed_items,
                }
                websocket_service.send_task_completed(task_id, summary)
                logger.info(
                    f"任务完成: {task_id}, 成功: {task_data.completed_items}, 失败: {task_data.failed_items}"
                )

        except Exception as e:
            logger.error(f"检查任务完成状态时出错: {str(e)}")

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            # 删除Redis记录
            redis_service.delete_task(task_id)

            logger.info(f"取消任务: {task_id}")
            return True

        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            return False


# 全局翻译任务服务实例
translation_task_service = TranslationTaskService()
