"""
任务管理器 - 处理任务ID生成和状态转换逻辑
"""

import uuid
from datetime import datetime
from typing import Dict, Any
from app.models.task_models import (
    TaskItemStatus,
    TaskItemData,
    TranslationTaskData,
    TaskStatus,
)


class TaskManager:
    """任务管理器"""

    @staticmethod
    def generate_task_id() -> str:
        """生成唯一的任务ID"""
        return str(uuid.uuid4())

    @staticmethod
    def create_task_data(task_id: str, items_keys: list[str]) -> TranslationTaskData:
        """创建新的任务数据结构"""
        now = datetime.now()

        # 初始化所有条目为processing状态
        items_data = {}
        for key in items_keys:
            items_data[key] = TaskItemData(
                status=TaskItemStatus.PROCESSING,
                result=None,
                error_message=None,
                updated_at=now,
            )

        return TranslationTaskData(
            task_id=task_id,
            status=TaskStatus.PROCESSING.value,
            total_items=len(items_keys),
            completed_items=0,
            failed_items=0,
            items=items_data,
            created_at=now,
            updated_at=now,
        )

    @staticmethod
    def update_item_status(
        task_data: TranslationTaskData,
        item_key: str,
        status: TaskItemStatus,
        result: Dict[str, Any] = None,
        error_message: str = None,
    ) -> TranslationTaskData:
        """更新单个条目的状态"""
        if item_key not in task_data.items:
            raise ValueError(f"Item key '{item_key}' not found in task")

        # 获取旧状态
        old_status = task_data.items[item_key].status

        # 更新条目状态
        task_data.items[item_key] = TaskItemData(
            status=status,
            result=result,
            error_message=error_message,
            updated_at=datetime.now(),
        )

        # 更新任务级别的计数器
        if old_status == TaskItemStatus.PROCESSING:
            if status == TaskItemStatus.SUCCESS:
                task_data.completed_items += 1
            elif status == TaskItemStatus.FAILED:
                task_data.failed_items += 1

        # 更新任务状态
        task_data.updated_at = datetime.now()

        # 检查是否所有条目都已完成
        total_finished = task_data.completed_items + task_data.failed_items
        if total_finished >= task_data.total_items:
            task_data.status = TaskStatus.COMPLETED.value

        return task_data

    @staticmethod
    def get_progress(task_data: TranslationTaskData) -> Dict[str, int]:
        """获取任务进度信息"""
        return {
            "completed": task_data.completed_items,
            "total": task_data.total_items,
            "failed": task_data.failed_items,
            "processing": task_data.total_items
            - task_data.completed_items
            - task_data.failed_items,
        }

    @staticmethod
    def is_task_completed(task_data: TranslationTaskData) -> bool:
        """检查任务是否已完成"""
        return task_data.status == TaskStatus.COMPLETED.value

    @staticmethod
    def is_task_processing(task_data: TranslationTaskData) -> bool:
        """检查任务是否正在处理中"""
        return task_data.status == TaskStatus.PROCESSING.value

    @staticmethod
    def get_completion_rate(task_data: TranslationTaskData) -> float:
        """获取任务完成率（0.0-1.0）"""
        if task_data.total_items == 0:
            return 1.0
        return task_data.completed_items / task_data.total_items
